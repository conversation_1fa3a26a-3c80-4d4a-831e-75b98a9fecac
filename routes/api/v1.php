<?php

use App\Http\Controllers\Api\V1\Auth;
use App\Http\Controllers\Api\V1\User;
use Illuminate\Support\Facades\Route;

/**
 * auth
 */
Route::post('/auth/login', [Auth\LoginController::class, '__invoke']);

/**
 * authenticated routes
 */
Route::middleware(['auth.jwt'])->group(function () {
    Route::post('/auth/logout', [Auth\LogoutController::class, '__invoke']);
    Route::get('/user/profile', [User\ProfileController::class, '__invoke']);
});
