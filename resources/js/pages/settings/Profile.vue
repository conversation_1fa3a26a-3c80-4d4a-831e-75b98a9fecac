<script setup lang="ts">
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';

import DeleteUser from '@/components/DeleteUser.vue';
import HeadingSmall from '@/components/HeadingSmall.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { type SharedData, type User } from '@/types';
import { useI18n } from 'vue-i18n';
import { useToast } from 'vue-toast-notification';
import { LoaderCircle } from 'lucide-vue-next';

const { t } = useI18n();
const toast = useToast();

interface Props {
    mustVerifyEmail: boolean;
    status?: string;
}

defineProps<Props>();

const page = usePage<SharedData>();
const user = page.props.auth.user as User;

const form = useForm({
    name: user.name,
    email: user.email,
});

const submit = () => {
    form.patch(route('profile.update'), {
        preserveScroll: true,
        onSuccess: () => {
            toast.success(t('profile.toast'), {
                position: 'top-right',
            })
        }
    });
};

const title = t('settings.profile');
</script>

<template>
    <AppLayout :title="title">
        <Head :title="title" />

        <SettingsLayout>
            <div class="flex flex-col space-y-6">
                <HeadingSmall :title="title" :description="t('profile.updateDescription')" />

                <div class="space-y-5">
                    <div class="grid gap-1">
                        <Label for="name">{{ t('profile.name') }}</Label>
                        <Input
                            id="name"
                            class="mt-1 block w-full"
                            v-model="form.name"
                            :placeholder="t('profile.name')"
                            :disabled="form.processing"
                            autofocus
                        />
                        <InputError class="mt-1" :message="form.errors.name" />
                    </div>

                    <div class="grid gap-1">
                        <Label for="email">{{ t('email.address') }}</Label>
                        <Input
                            id="email"
                            type="email"
                            class="mt-1 block w-full"
                            v-model="form.email"
                            :placeholder="t('email.address')"
                            :disabled="form.processing"
                        />
                        <InputError class="mt-1" :message="form.errors.email" />
                    </div>

                    <div v-if="mustVerifyEmail && !user.email_verified_at">
                        <p class="text-muted-foreground -mt-4 text-sm">
                            {{ t('email.unverified') }}
                            <Link
                                :href="route('verification.send')"
                                method="post"
                                as="button"
                                class="text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"
                            >
                                {{ t('email.sendVerificationEmail') }}
                            </Link>
                        </p>

                        <div v-if="status === 'verification-link-sent'" class="mt-2 text-sm font-medium text-green-600">
                            {{ t('email.sendVerificationEmailDone') }}
                        </div>
                    </div>

                    <div class="flex items-center gap-4">
                        <Button :disabled="form.processing" type="button" @click="submit">
                            <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin" />
                            {{ t('save') }}
                        </Button>

                        <Transition
                            enter-active-class="transition ease-in-out"
                            enter-from-class="opacity-0"
                            leave-active-class="transition ease-in-out"
                            leave-to-class="opacity-0"
                        >
                            <p v-show="form.recentlySuccessful" class="text-sm text-green-700">{{ t('saved') }}</p>
                        </Transition>
                    </div>
                </div>
            </div>

            <div class="h-px border-t border-gray-300 dark:border-[var(--sidebar-border-color)]" />

            <DeleteUser />
        </SettingsLayout>
    </AppLayout>
</template>
