<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import AppLayout from '@/layouts/app/AppLayout.vue';
import SettingsLayout from '@/layouts/settings/Layout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

import HeadingSmall from '@/components/HeadingSmall.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useI18n } from 'vue-i18n';
import { useToast } from 'vue-toast-notification';
import { LoaderCircle } from 'lucide-vue-next';

const { t } = useI18n();
const toast = useToast();

interface Props {
    hasCurrentPassword: boolean;
}

defineProps<Props>();

const passwordInput = ref<HTMLInputElement | null>(null);
const currentPasswordInput = ref<HTMLInputElement | null>(null);

const form = useForm({
    current_password: '',
    password: '',
    password_confirmation: '',
});

const updatePassword = () => {
    form.put(route('password.update'), {
        preserveScroll: true,
        onSuccess: () => form.reset(),
        onError: (errors: any) => {
            if (errors.new_password) {
                form.reset('password', 'password_confirmation');
                if (passwordInput.value instanceof HTMLInputElement) {
                    passwordInput.value.focus();
                }
            }

            if (errors.current_password) {
                form.reset('current_password');
                if (currentPasswordInput.value instanceof HTMLInputElement) {
                    currentPasswordInput.value.focus();
                }
            }
        },
        onFinish: () => {
            if (form.recentlySuccessful) {
                toast.success(t('password.changeMessage'), {
                    position: 'top-right',
                })
            }
        },
    });
};

const title = t('password.change');
</script>

<template>
    <AppLayout :title="title">
        <Head :title="title" />

        <SettingsLayout>
            <div class="space-y-6">
                <HeadingSmall :title="title" :description="t('password.changeDescription')" />

                <div class="space-y-5">
                    <div class="grid gap-1" v-if="hasCurrentPassword">
                        <Label for="current_password">{{ t('password.current') }}</Label>
                        <Input
                            id="current_password"
                            ref="currentPasswordInput"
                            v-model="form.current_password"
                            type="password"
                            class="mt-1 block w-full"
                            :placeholder="t('password.current')"
                            :disabled="form.processing"
                        />
                        <InputError :message="form.errors.current_password" />
                    </div>

                    <div class="grid gap-1">
                        <Label for="password">{{ t('password.new') }}</Label>
                        <Input
                            id="password"
                            ref="passwordInput"
                            v-model="form.password"
                            type="password"
                            class="mt-1 block w-full"
                            :placeholder="t('password.new')"
                            :disabled="form.processing"
                        />
                        <InputError :message="form.errors.password" />
                    </div>

                    <div class="grid gap-1">
                        <Label for="password_confirmation">{{ t('password.confirm') }}</Label>
                        <Input
                            id="password_confirmation"
                            v-model="form.password_confirmation"
                            type="password"
                            class="mt-1 block w-full"
                            :placeholder="t('password.confirm')"
                            :disabled="form.processing"
                        />
                        <InputError :message="form.errors.password_confirmation" />
                    </div>

                    <div class="flex items-center">
                        <Button :disabled="form.processing" type="button" @click="updatePassword">
                            <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin" />
                            {{ t('form.save') }}
                        </Button>
                    </div>
                </div>
            </div>
        </SettingsLayout>
    </AppLayout>
</template>
