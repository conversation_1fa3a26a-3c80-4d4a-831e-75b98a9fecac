<script setup lang="ts">
import { computed, ref, type HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import {
  SelectRoot,
  SelectTrigger,
  SelectValue,
  SelectIcon,
  SelectPortal,
  SelectContent,
  SelectViewport,
  SelectItem,
  SelectItemText,
  SelectItemIndicator,
} from 'reka-ui'
import { ChevronDown, X, RefreshCw, Check } from 'lucide-vue-next'
import { type SelectBoxOption, type SelectBoxVariants, selectBoxVariants } from '.'

interface Props {
  modelValue?: string | number
  class?: HTMLAttributes['class']
  options?: SelectBoxOption[]
  placeholder?: string
  showLabel?: boolean
  label?: string
  showClear?: boolean
  showRefresh?: boolean
  refreshLoading?: boolean
  disabled?: boolean
  error?: boolean
  variant?: SelectBoxVariants['variant']
  size?: SelectBoxVariants['size']
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  showClear: true,
  showRefresh: false,
  refreshLoading: false,
  disabled: false,
  error: false,
  placeholder: '',
  variant: 'default',
  size: 'default',
  options: () => [],
})

const emits = defineEmits<{
  'update:modelValue': [value: any]
  'refresh': []
  'clear': []
}>()

const hasValue = computed(() => {
  return props.modelValue !== undefined && props.modelValue !== null && props.modelValue !== ''
})

const handleClear = () => {
  emits('update:modelValue', undefined)
  emits('clear')
}

const handleRefresh = () => {
  emits('refresh')
}

const isOpening = ref(false);
</script>

<template>
  <div :class="cn('w-full space-y-1', props.class)">
    <!-- Label -->
    <label
      v-if="showLabel && label"
      :class="cn(
        'font-semibold leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        error ? 'text-destructive' : 'text-foreground'
      )"
    >
      {{ label }}
    </label>

    <!-- Select Component -->
    <div class="relative">
      <SelectRoot
        :model-value="String(modelValue || '')"
        :disabled="refreshLoading || disabled || options.length === 0"
        :open="isOpening"
        @update:open="isOpening = $event"
        @update:model-value="emits('update:modelValue', $event)"
      >
        <SelectTrigger
          :class="cn(
            selectBoxVariants({ variant: error ? 'error' : variant, size }),
            'flex items-center justify-between',
            isOpening ? 'selectbox-active' : '',
          )"
          :disabled="refreshLoading || disabled || options.length === 0"
        >
          <div :class="cn(
            'flex items-center flex-1 min-w-0 truncate',
            showRefresh ? ((showClear && hasValue) ? 'mr-12' : 'mr-8') : ((showClear && hasValue) ? 'mr-8' : 'mr-1'),
          )">
            <SelectValue
              :class="cn(
                !hasValue ? 'text-gray-400 dark:text-[var(--input-placeholder)]' : ''
              )"
              :placeholder="placeholder"
             />
          </div>

          <!-- Chevron Down - Inside SelectTrigger to work properly -->
          <SelectIcon>
            <ChevronDown class="h-4 w-4 text-gray-600 dark:text-gray-400" />
          </SelectIcon>
        </SelectTrigger>

        <SelectPortal>
          <SelectContent
            position="popper"
            side="bottom"
            :side-offset="4"
            :class="cn(
              'relative z-50 max-h-96 overflow-hidden rounded-md border border-gray-300 dark:border-[var(--input-border)] bg-white dark:bg-zinc-900 shadow-md',
              'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
              'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
              'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2',
              'data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
              'w-[var(--reka-select-trigger-width)]'
            )"
          >
            <SelectViewport class="p-1">
              <SelectItem
                v-for="option in options"
                :key="option.value"
                :value="String(option.value)"
                :disabled="option.disabled"
                :class="cn(
                  'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 outline-none',
                  'transition-colors duration-200 hover:bg-neutral-300/40 dark:hover:bg-neutral-700/40',
                  'focus:bg-accent focus:text-accent-foreground',
                  'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
                  'data-[highlighted]:bg-accent data-[highlighted]:text-accent-foreground'
                )"
              >
                <span class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                  <SelectItemIndicator>
                    <Check class="h-4 w-4" />
                  </SelectItemIndicator>
                </span>

                <SelectItemText>
                  {{ option.label }}
                </SelectItemText>
              </SelectItem>
            </SelectViewport>
          </SelectContent>
        </SelectPortal>
      </SelectRoot>

      <!-- Action Buttons - Between SelectValue and SelectIcon -->
      <div class="absolute right-8 top-1/2 -translate-y-1/2 flex items-center gap-1 pointer-events-none">
        <!-- Clear Button -->
        <button
          v-if="showClear && hasValue"
          type="button"
          @click="handleClear"
          :class="cn(
            'inline-flex items-center justify-center rounded-sm w-4 h-4 text-muted-foreground hover:text-foreground transition-colors',
            'hover:bg-muted pointer-events-auto'
          )"
        >
          <X class="h-3 w-3 text-gray-600 dark:text-gray-400" />
        </button>

        <!-- Refresh Button -->
        <button
          v-if="showRefresh"
          type="button"
          @click="handleRefresh"
          :disabled="refreshLoading"
          :class="cn(
            'inline-flex items-center justify-center rounded-sm w-4 h-4 text-muted-foreground hover:text-foreground transition-colors',
            'hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed pointer-events-auto',
            refreshLoading && 'animate-spin'
          )"
        >
          <RefreshCw class="h-3 w-3 text-gray-600 dark:text-gray-400" />
        </button>
      </div>
    </div>
  </div>
</template>
