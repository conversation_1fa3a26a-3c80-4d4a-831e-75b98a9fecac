import { cva, type VariantProps } from 'class-variance-authority'

export interface SelectBoxOption {
  value: string | number
  label: string
  disabled?: boolean
}

export { default as SelectBox } from './SelectBox.vue'

export const selectBoxVariants = cva(
  'selectbox',
  {
    variants: {
      variant: {
        default: 'selectbox-default',
        error: 'selectbox-default',
      },
      size: {
        default: 'h-10',
        sm: 'h-9 px-2 text-xs',
        lg: 'h-11 px-4',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
)

export type SelectBoxVariants = VariantProps<typeof selectBoxVariants>
