<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

// Components
import HeadingSmall from '@/components/HeadingSmall.vue';
import InputError from '@/components/InputError.vue';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useI18n } from 'vue-i18n';
import { LoaderCircle } from 'lucide-vue-next';

const { t } = useI18n();
const passwordInput = ref<HTMLInputElement | null>(null);

const form = useForm({
    password: '',
});

const deleteUser = (e: Event) => {
    e.preventDefault();

    form.delete(route('profile.destroy'), {
        preserveScroll: true,
        onSuccess: () => closeModal(),
        onFinish: () => form.reset(),
    });
};

const closeModal = () => {
    form.clearErrors();
    form.reset();
};
</script>

<template>
    <div class="space-y-6">
        <HeadingSmall :title="t('account.delete')" :description="t('account.deleteDescription')" />
        <div class="space-y-4 rounded-lg border border-red-200 bg-red-100 p-4 dark:border-red-200/10 dark:bg-red-700/10">
            <div class="relative space-y-0.5 text-gray-800 dark:text-gray-200">
                <p class="font-medium" v-text="t('warning')" />
                <p class="text-sm" v-text="t('account.deleteWarning')" />
            </div>
            <Dialog>
                <DialogTrigger as-child>
                    <Button variant="destructive">{{ t('account.deleteButton') }}</Button>
                </DialogTrigger>
                <DialogContent>
                    <div class="space-y-6">
                        <DialogHeader class="space-y-3">
                            <DialogTitle>{{ t('account.deleteConfirmTitle') }}</DialogTitle>
                            <DialogDescription>{{ t('account.deleteConfirmMessage') }}</DialogDescription>
                        </DialogHeader>

                        <div class="grid gap-1">
                            <Label for="password" class="sr-only">{{ t('password.current') }}</Label>
                            <Input
                                id="password"
                                type="password"
                                name="password"
                                ref="passwordInput"
                                v-model="form.password"
                                :placeholder="t('password.current')"
                            />
                            <InputError :message="form.errors.password" />
                        </div>

                        <DialogFooter class="gap-2">
                            <DialogClose as-child>
                                <Button variant="secondary" @click="closeModal">{{ t('cancel') }}</Button>
                            </DialogClose>

                            <Button type="button" variant="destructive" :disabled="form.processing" @click="deleteUser">
                                <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin mr-1" />
                                {{ t('account.delete') }}
                            </Button>
                        </DialogFooter>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    </div>
</template>
