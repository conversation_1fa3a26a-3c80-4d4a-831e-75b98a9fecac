<script setup lang="ts">
import UserInfo from '@/components/UserInfo.vue';
import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import type { User } from '@/types';
import { Link, router } from '@inertiajs/vue3';
import { LoaderCircle, LogOut, Settings } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ref } from 'vue';

const { t } = useI18n();

interface Props {
    user: User;
}

const loggingOut = ref(false);

const handleLogout = () => {
    if (loggingOut.value) {
        return;
    }

    router.post(route('logout'), {}, {
        onStart: () => {
            loggingOut.value = true;
        },
        onSuccess: () => {
            loggingOut.value = false;
            router.flushAll();
        },
    });
};

defineProps<Props>();
</script>

<template>
    <DropdownMenuLabel class="p-0 font-normal">
        <div class="user-info-wrapper">
            <UserInfo :user="user" :show-email="true" />
        </div>
    </DropdownMenuLabel>
    <DropdownMenuSeparator />
    <DropdownMenuGroup>
        <DropdownMenuItem :as-child="true">
            <Link
                class="dropdown-menu-link"
                :href="route('profile.edit')" prefetch
                as="button"
            >
                <Settings class="mr-2 h-4 w-4" />
                {{ t('settings.label') }}
            </Link>
        </DropdownMenuItem>
    </DropdownMenuGroup>
    <DropdownMenuSeparator />
    <DropdownMenuItem :as-child="true">
        <Dialog>
            <DialogTrigger as-child>
                <button class="dropdown-menu-link gap-2 rounded-sm px-2 py-1.5 text-sm">
                    <LogOut class="mr-2 h-4 w-4" />
                    {{ t('logout.label') }}
                </button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader class="space-y-3">
                    <DialogTitle>{{ t('confirmation.title') }}</DialogTitle>
                    <DialogDescription>{{ t('logout.confirmationMessage') }}</DialogDescription>
                </DialogHeader>

                <DialogFooter class="gap-2">
                    <DialogClose as-child>
                        <Button variant="secondary" size="sm" class="bg-gray-200 hover:bg-gray-300 text-gray-500 hover:text-gray-700">{{ t('cancel') }}</Button>
                    </DialogClose>

                    <Button type="button" variant="destructive" size="sm" :disabled="loggingOut" @click="handleLogout">
                        <LoaderCircle v-if="loggingOut" class="h-4 w-4 animate-spin mr-1" />
                        {{ t('logout.label') }}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    </DropdownMenuItem>
</template>
